<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Marketing Insights - 4 Key Reasons</title>
    <link href="https://fonts.googleapis.com/css2?family=Clash+Display:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            background-color: #F8F9FA;
            color: #1E1E1E;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        h1 {
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            font-size: 3.5rem;
            text-align: center;
            margin-bottom: 20px;
            text-transform: uppercase;
            background: linear-gradient(135deg, #2B203B, #BDEE96);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #2B203B;
        }

        h3 {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 15px;
            color: #2B203B;
        }

        .intro {
            text-align: center;
            margin-bottom: 60px;
            font-size: 1.1rem;
            color: #666;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .chart-card {
            background: #FFFFFF;
            padding: 24px;
            border: 1px solid #E9ECEF;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .chart-description {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #BDEE96 0%, #BDEE96 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.2em;
            background-position: 0 88%;
            font-weight: 600;
            color: #2B203B;
        }

        .stats-summary {
            background: #2B203B;
            color: #FFFFFF;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            margin-top: 40px;
        }

        .stats-summary h2 {
            color: #BDEE96;
            margin-bottom: 20px;
        }

        .key-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #BDEE96;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }

            h2 {
                font-size: 2rem;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Digital Marketing Insights</h1>
        <div class="intro">
            <p>Discover the <span class="highlight">4 key reasons</span> why digital marketing consulting is essential for modern businesses. These data-driven insights reveal critical trends shaping consumer behavior and business success.</p>
        </div>

        <div class="chart-grid">
            <div class="chart-card">
                <h3>1. Mobile Search Dominance by Generation</h3>
                <div class="chart-container">
                    <canvas id="mobileSearchChart"></canvas>
                </div>
                <div class="chart-description">
                    <strong>80% of Gen Z</strong> primarily use mobile search, highlighting the critical importance of mobile optimization. Instagram and YouTube are key platforms for brand discovery across all generations.
                </div>
            </div>

            <div class="chart-card">
                <h3>2. Content Marketing Lead Generation</h3>
                <div class="chart-container">
                    <canvas id="blogLeadsChart"></canvas>
                </div>
                <div class="chart-description">
                    Companies that blog regularly generate <strong>67% more leads</strong> per month than those that don't. Consistent content marketing builds stronger audience connections and converts traffic into valuable leads.
                </div>
            </div>

            <div class="chart-card">
                <h3>3. Social Media Impact on Gen Z</h3>
                <div class="chart-container">
                    <canvas id="socialMediaChart"></canvas>
                </div>
                <div class="chart-description">
                    <strong>97% of Gen Z</strong> use social media as their top source of shopping inspiration, with 65% seeking entertaining content and 61% preferring video content. Social platforms are crucial for engaging younger demographics.
                </div>
            </div>

            <div class="chart-card">
                <h3>4. UX Design Revenue Impact</h3>
                <div class="chart-container">
                    <canvas id="uxRevenueChart"></canvas>
                </div>
                <div class="chart-description">
                    Businesses investing in UX design see a <strong>32% average revenue increase</strong>. Prioritizing user experience significantly impacts the bottom line, boosting both revenue and engagement.
                </div>
            </div>
        </div>

        <div class="stats-summary">
            <h2>Key Takeaways</h2>
            <p>These insights demonstrate the critical importance of digital marketing consulting in today's competitive landscape.</p>
            <div class="key-stats">
                <div class="stat-item">
                    <span class="stat-number">80%</span>
                    <span class="stat-label">Gen Z Mobile Search</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">67%</span>
                    <span class="stat-label">More Leads from Blogging</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">97%</span>
                    <span class="stat-label">Gen Z Social Shopping</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">32%</span>
                    <span class="stat-label">UX Revenue Increase</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.font.family = 'Plus Jakarta Sans';
        Chart.defaults.color = '#1E1E1E';

        const chartColors = {
            primary: '#2B203B',
            accent: '#BDEE96',
            secondary: '#E9ECEF',
            gradient: ['#2B203B', '#BDEE96', '#8B7BA8', '#A8D478']
        };

        // Mobile Search Chart
        const mobileSearchCtx = document.getElementById('mobileSearchChart').getContext('2d');
        new Chart(mobileSearchCtx, {
            type: 'doughnut',
            data: {
                labels: ['Gen Z', 'Millennials', 'Gen X', 'Boomers'],
                datasets: [{
                    data: [80, 62, 66, 35],
                    backgroundColor: chartColors.gradient,
                    borderWidth: 2,
                    borderColor: '#FFFFFF'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Blog Leads Chart
        const blogLeadsCtx = document.getElementById('blogLeadsChart').getContext('2d');
        new Chart(blogLeadsCtx, {
            type: 'bar',
            data: {
                labels: ['Companies with Regular Blogging', 'Companies without Regular Blogging'],
                datasets: [{
                    label: 'Lead Generation',
                    data: [167, 100],
                    backgroundColor: [chartColors.accent, chartColors.secondary],
                    borderColor: [chartColors.primary, '#999'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Relative Lead Generation (%)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Social Media Chart
        const socialMediaCtx = document.getElementById('socialMediaChart').getContext('2d');
        new Chart(socialMediaCtx, {
            type: 'bar',
            data: {
                labels: ['Shopping Inspiration', 'Entertaining Content', 'Video Content'],
                datasets: [{
                    label: 'Gen Z Usage (%)',
                    data: [97, 65, 61],
                    backgroundColor: [chartColors.accent, chartColors.primary, '#8B7BA8'],
                    borderColor: chartColors.primary,
                    borderWidth: 2
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // UX Revenue Chart
        const uxRevenueCtx = document.getElementById('uxRevenueChart').getContext('2d');
        new Chart(uxRevenueCtx, {
            type: 'bar',
            data: {
                labels: ['Without UX Investment', 'With UX Investment'],
                datasets: [{
                    label: 'Revenue Index',
                    data: [100, 132],
                    backgroundColor: [chartColors.secondary, chartColors.accent],
                    borderColor: [chartColors.primary, chartColors.primary],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Revenue Index'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>