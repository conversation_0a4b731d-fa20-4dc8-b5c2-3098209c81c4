<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KD Digital Marketing Consulting</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Clash+Display:wght@600&family=Plus+Jakarta+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      --primary: #2B203B;
      --accent: #BDEE96;
      --bg: #F8F9FA;
      --surface: #FFFFFF;
      --text-dark: #1E1E1E;
      --text-light: #FFFFFF;
      --border: #E9ECEF;
      --shadow-soft: 0 4px 12px rgba(0,0,0,0.05);
      --shadow-lifted: 0 12px 28px rgba(43,32,59,0.15);
      --radius-card: 12px;
      --radius-btn: 8px;
      --font-heading: 'Clash Display', 'Clash Display Variable', Arial, sans-serif;
      --font-body: 'Plus Jakarta Sans', Arial, sans-serif;
      --transition-speed: 0.3s ease;
    }
    html, body {
      margin: 0;
      padding: 0;
      background: var(--bg);
      color: var(--text-dark);
      font-family: var(--font-body);
      font-size: 16px;
      line-height: 1.7;
      scroll-behavior: smooth;
    }
    header {
      position: relative;
      background: var(--primary);
      color: var(--text-light);
      padding: 0;
      min-height: 420px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    .header-bg {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      background: url('https://picsum.photos/1200/600?blur=2') center center/cover no-repeat;
    }
    .header-overlay {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(120deg, rgba(43,32,59,0.82) 60%, rgba(43,32,59,0.7) 100%);
      z-index: 2;
    }
    .header-content {
      position: relative;
      z-index: 3;
      padding: 100px 24px 64px 24px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .hero-title {
      font-family: var(--font-heading);
      font-size: 3.8rem;
      font-weight: 600;
      letter-spacing: 1.5px;
      text-transform: uppercase;
      margin: 0 0 12px 0;
      color: var(--text-light);
      text-shadow: 0 4px 24px rgba(43,32,59,0.25);
      text-align: center;
    }
    .subtitle {
      font-family: var(--font-body);
      font-size: 1.3rem;
      color: var(--accent);
      font-weight: 500;
      margin-bottom: 0;
      text-align: center;
      text-shadow: 0 2px 8px rgba(43,32,59,0.18);
    }
    main {
      max-width: 1140px;
      margin: 0 auto;
      padding: 64px 24px 0 24px;
    }
    section {
      margin-bottom: 100px;
    }
    h2 {
      font-family: var(--font-heading);
      font-size: 2.8rem;
      font-weight: 600;
      color: var(--primary);
      margin-top: 0;
      margin-bottom: 40px;
      text-align: center;
    }
    .intro-text {
      max-width: 720px;
      margin: 0 auto 48px auto;
      text-align: center;
      font-size: 1.1rem;
      line-height: 1.8;
    }
    .services-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 32px;
      margin-top: 48px;
    }
    .service {
      background: var(--surface);
      border: 1px solid var(--border);
      border-radius: var(--radius-card);
      box-shadow: var(--shadow-soft);
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      transition: transform var(--transition-speed), box-shadow var(--transition-speed), border-color var(--transition-speed);
      min-height: auto; /* Adjust min-height as content will vary */
      overflow: hidden; /* Ensure image corners are rounded with card */
    }
    .service:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-lifted);
      border-color: var(--accent);
    }
    .service img {
      width: calc(100% - 40px); /* Adjust width for padding */
      height: 200px;
      border-radius: var(--radius-card);
      margin: 20px; /* Margin around the image */
      box-shadow: none;
      object-fit: cover;
      background-color: var(--background);
    }
    .service-content {
      padding: 24px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex-grow: 1;
    }
    .service-title {
      font-family: var(--font-body);
      font-weight: 700;
      font-size: 1.35rem;
      margin: 0 0 12px 0;
      color: var(--primary);
    }
    .service p {
      font-family: var(--font-body);
      font-size: 1rem;
      color: var(--text-dark);
      margin: 0;
      line-height: 1.6;
    }
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 32px;
      margin-top: 48px;
    }
    .stat {
      background: var(--surface);
      border: 1px solid var(--border);
      border-left: 5px solid var(--accent);
      border-radius: var(--radius-btn);
      padding: 24px;
      text-align: left;
      box-shadow: var(--shadow-soft);
      transition: box-shadow var(--transition-speed);
    }
    .stat:hover {
      box-shadow: 0 8px 18px rgba(0,0,0,0.08);
    }
    .stat strong {
      font-family: var(--font-body);
      font-weight: 700;
      font-size: 1.3rem;
      color: var(--primary);
      display: block;
      margin-bottom: 8px;
    }
    .stat span {
      font-size: 0.95rem;
      color: var(--text-dark);
      line-height: 1.6;
    }
    .cta {
      background: var(--primary);
      color: var(--text-light);
      padding: 80px 40px;
      border-radius: var(--radius-card);
      text-align: center;
      margin-top: 60px;
      box-shadow: 0 8px 30px rgba(43,32,59,0.2);
    }
    .cta h2 {
      color: var(--accent);
      font-family: var(--font-heading);
      font-size: 2.8rem;
      margin-bottom: 16px;
      font-weight: 600;
    }
    .cta p {
      font-family: var(--font-body);
      font-size: 1.15rem;
      color: var(--text-light);
      max-width: 680px;
      margin: 0 auto 24px auto;
      line-height: 1.8;
    }
    .cta button {
      background: var(--accent);
      color: var(--text-dark);
      border: none;
      font-family: var(--font-body);
      font-size: 1.1rem;
      font-weight: 500;
      padding: 16px 32px;
      border-radius: var(--radius-btn);
      margin-top: 16px;
      cursor: pointer;
      transition: background var(--transition-speed), transform var(--transition-speed), box-shadow var(--transition-speed);
      box-shadow: 0 4px 15px rgba(189, 238, 150, 0.25);
      letter-spacing: 0.5px;
    }
    .cta button:hover {
      background: #c3e87c;
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(189, 238, 150, 0.4);
    }
    @media (max-width: 768px) {
      .hero-title { font-size: 2.3rem; }
      .subtitle { font-size: 1.1rem; }
      h2 { font-size: 2.2rem; }
      .intro-text { font-size: 1rem; }
      .services-list, .stats { grid-template-columns: 1fr; }
      .service { min-height: auto; padding: 24px; }
      .service img { width: 70px; height: 70px; }
      .service-title { font-size: 1.2rem; }
      .stat { padding: 20px; }
      .stat strong { font-size: 1.15rem; }
      .cta { padding: 60px 20px; }
      .cta h2 { font-size: 2.2rem; }
      .cta p { font-size: 1rem; }
      .cta button { padding: 14px 28px; font-size: 1rem; }
    }
  </style>
    <style>
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .chart-card {
            background: var(--surface);
            padding: 24px;
            border: 1px solid var(--border);
            border-radius: var(--radius-card);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .chart-description {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.6;
        }

        .chart-card h3 {
            font-family: var(--font-body);
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 15px;
            color: var(--primary);
        }

        .stats-summary {
            background: var(--primary);
            color: var(--text-light);
            padding: 40px;
            border-radius: var(--radius-card);
            text-align: center;
            margin-top: 40px;
        }

        .stats-summary h3 {
            color: var(--accent);
            margin-bottom: 20px;
            font-family: var(--font-heading);
            font-size: 2rem;
        }

        .key-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent);
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    </head>
    <body>
    <header>
      <div class="header-bg" aria-hidden="true"></div>
      <div class="header-overlay" aria-hidden="true"></div>
      <div class="header-content">
        <div class="hero-title">DIGITAL MARKETING CONSULTING</div>
        <div class="subtitle">Rooted in Data, Culture &amp; Connection</div>
      </div>
    </header>
    <main>
      <section id="intro">
        <p class="intro-text">In the ever-evolving digital landscape, a robust online presence is essential. At <strong>KD Digital</strong>, we simplify the complexities of digital marketing, offering tailored consulting services to elevate your brand, boost visibility, and drive sustainable growth. Whether you're a startup or an established business, our experts guide you every step of the way.</p>
      </section>
      <section id="services">
        <h2>Our Services</h2>
        <div class="services-list">
          <div class="service">
            <img src="https://images.pexels.com/photos/5716032/pexels-photo-5716032.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Digital Marketing Strategy Development">
            <div class="service-content">
              <div class="service-title">Digital Marketing Strategy Development</div>
              <p>We craft tailored strategies aligned with your business objectives, identifying the right channels, content, and tactics to effectively reach your target audience.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/270637/pexels-photo-270637.jpeg?auto=compress&cs=tinysrgb&h=350" alt="SEO Audits and Optimization">
            <div class="service-content">
              <div class="service-title">SEO Audits and Optimization</div>
              <p>Our thorough SEO audits pinpoint improvements. We implement optimization techniques, from keyword research to on-page tweaks, to make your website search-engine friendly and boost rankings.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/7661185/pexels-photo-7661185.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Content Marketing Strategy">
            <div class="service-content">
              <div class="service-title">Content Marketing Strategy</div>
              <p>Develop a content strategy that resonates with your audience and drives engagement. We plan blog posts, videos, and social media content to enhance your brand’s voice and authority.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/2882669/pexels-photo-2882669.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Social Media Marketing Consultation">
            <div class="service-content">
              <div class="service-title">Social Media Marketing Consultation</div>
              <p>Gain insights and strategies to elevate your social media presence. We cover content planning to engagement tactics for platforms like Instagram, Facebook, and LinkedIn.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/3584994/pexels-photo-3584994.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Website & User Experience (UX) Consulting">
            <div class="service-content">
              <div class="service-title">Website &amp; User Experience (UX) Consulting</div>
              <p>Ensure your website offers a seamless, enjoyable experience. Our experts assess design, navigation, and functionality to improve user satisfaction and conversions.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/5716001/pexels-photo-5716001.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Competitor Analysis & Market Research">
            <div class="service-content">
              <div class="service-title">Competitor Analysis &amp; Market Research</div>
              <p>We conduct comprehensive competitor analysis and market research to identify opportunities, providing data-driven strategies for a competitive edge.</p>
            </div>
          </div>
          <div class="service">
            <img src="https://images.pexels.com/photos/669610/pexels-photo-669610.jpeg?auto=compress&cs=tinysrgb&h=350" alt="Analytics & Performance Reporting">
            <div class="service-content">
              <div class="service-title">Analytics &amp; Performance Reporting</div>
              <p>Track your digital marketing effectiveness with detailed analytics and performance reports. Gain insights into what’s working and where adjustments are needed for continuous improvement.</p>
            </div>
          </div>
        </div>
      </section>
      <section id="why-us">
        <h2>4 Reasons to Consider the Importance of Digital Marketing Consulting</h2>

        <div class="chart-grid">
            <div class="chart-card">
                <h3>1. Mobile Search Dominance by Generation</h3>
                <div class="chart-container">
                    <canvas id="mobileSearchChart"></canvas>
                </div>
                <div class="chart-description">
                    <strong>80% of Gen Z</strong> primarily use mobile search, highlighting the critical importance of mobile optimization. Instagram and YouTube are key platforms for brand discovery across all generations.
                </div>
            </div>

            <div class="chart-card">
                <h3>2. Content Marketing Lead Generation</h3>
                <div class="chart-container">
                    <canvas id="blogLeadsChart"></canvas>
                </div>
                <div class="chart-description">
                    Companies that blog regularly generate <strong>67% more leads</strong> per month than those that don't. Consistent content marketing builds stronger audience connections and converts traffic into valuable leads.
                </div>
            </div>

            <div class="chart-card">
                <h3>3. Social Media Impact on Gen Z</h3>
                <div class="chart-container">
                    <canvas id="socialMediaChart"></canvas>
                </div>
                <div class="chart-description">
                    <strong>97% of Gen Z</strong> use social media as their top source of shopping inspiration, with 65% seeking entertaining content and 61% preferring video content. Social platforms are crucial for engaging younger demographics.
                </div>
            </div>

            <div class="chart-card">
                <h3>4. UX Design Revenue Impact</h3>
                <div class="chart-container">
                    <canvas id="uxRevenueChart"></canvas>
                </div>
                <div class="chart-description">
                    Businesses investing in UX design see a <strong>32% average revenue increase</strong>. Prioritizing user experience significantly impacts the bottom line, boosting both revenue and engagement.
                </div>
            </div>
        </div>

        <div class="stats-summary">
            <h3>Key Takeaways</h3>
            <p>These insights demonstrate the critical importance of digital marketing consulting in today's competitive landscape.</p>
            <div class="key-stats">
                <div class="stat-item">
                    <span class="stat-number">80%</span>
                    <span class="stat-label">Gen Z Mobile Search</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">67%</span>
                    <span class="stat-label">More Leads from Blogging</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">97%</span>
                    <span class="stat-label">Gen Z Social Shopping</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">32%</span>
                    <span class="stat-label">UX Revenue Increase</span>
                </div>
            </div>
        </div>
      </section>
      <section class="cta" id="contact">
        <h2>Ready to Elevate Your Digital Presence?</h2>
        <p>At KD Digital, we're more than consultants—we're your strategic partners in growth. Our personalized approach ensures every facet of your digital marketing is optimized for success. Let's craft a strategy that delivers real results.</p>
        <button onclick="alert('Thank you for your interest! We will contact you soon to schedule your free consultation.');">Schedule Your Free Consultation</button>
      </section>
    
    </main>

    <script>
        // Chart.js configuration
        Chart.defaults.font.family = 'Plus Jakarta Sans';
        Chart.defaults.color = '#1E1E1E';

        const chartColors = {
            primary: '#2B203B',
            accent: '#BDEE96',
            secondary: '#E9ECEF',
            gradient: ['#2B203B', '#BDEE96', '#8B7BA8', '#A8D478']
        };

        // Mobile Search Chart
        const mobileSearchCtx = document.getElementById('mobileSearchChart').getContext('2d');
        new Chart(mobileSearchCtx, {
            type: 'doughnut',
            data: {
                labels: ['Gen Z', 'Millennials', 'Gen X', 'Boomers'],
                datasets: [{
                    data: [80, 62, 66, 35],
                    backgroundColor: chartColors.gradient,
                    borderWidth: 2,
                    borderColor: '#FFFFFF'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Blog Leads Chart
        const blogLeadsCtx = document.getElementById('blogLeadsChart').getContext('2d');
        new Chart(blogLeadsCtx, {
            type: 'bar',
            data: {
                labels: ['Companies with Regular Blogging', 'Companies without Regular Blogging'],
                datasets: [{
                    label: 'Lead Generation',
                    data: [167, 100],
                    backgroundColor: [chartColors.accent, chartColors.secondary],
                    borderColor: [chartColors.primary, '#999'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Relative Lead Generation (%)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Social Media Chart
        const socialMediaCtx = document.getElementById('socialMediaChart').getContext('2d');
        new Chart(socialMediaCtx, {
            type: 'bar',
            data: {
                labels: ['Shopping Inspiration', 'Entertaining Content', 'Video Content'],
                datasets: [{
                    label: 'Gen Z Usage (%)',
                    data: [97, 65, 61],
                    backgroundColor: [chartColors.accent, chartColors.primary, '#8B7BA8'],
                    borderColor: chartColors.primary,
                    borderWidth: 2
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Percentage (%)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // UX Revenue Chart
        const uxRevenueCtx = document.getElementById('uxRevenueChart').getContext('2d');
        new Chart(uxRevenueCtx, {
            type: 'bar',
            data: {
                labels: ['Without UX Investment', 'With UX Investment'],
                datasets: [{
                    label: 'Revenue Index',
                    data: [100, 132],
                    backgroundColor: [chartColors.secondary, chartColors.accent],
                    borderColor: [chartColors.primary, chartColors.primary],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Revenue Index'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>

  </body>
</html>
