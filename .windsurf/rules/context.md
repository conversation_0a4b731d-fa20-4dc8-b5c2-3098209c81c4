---
trigger: always_on
---

Website Style Guide
This guide provides specifications for the visual style of the website. Following these guidelines will ensure a consistent and professional look across the entire platform.

1. Typography
We use two primary fonts: Clash Display for headings to create impact, and Plus Jakarta Sans for body text to ensure readability.

Fonts
Headings Font: Clash Display Variable

Body Font: Plus Jakarta Sans

You can import these fonts from Google Fonts or other font providers.

Font Hierarchy
Element

Font Family

Font Weight

Recommended Size

Notes

H1 (Main Heading)

Clash Display Variable

600 (Semibold)

3.5rem (56px)

Used for hero section titles. ALL CAPS.

H2 (Section Heading)

Clash Display Variable

600 (Semibold)

2.5rem (40px)

Used for titles like "What Makes Us Different".

H3 (Card Title)

Plus Jakarta Sans

700 (Bold)

1.25rem (20px)

For titles inside service or case study cards.

Body / Paragraph

Plus Jakarta Sans

400 (Regular)

1rem (16px)

Standard text for descriptions and paragraphs.

Button / Link Text

Plus Jakarta Sans

500 (Medium)

1rem (16px)

For all interactive text elements.

Labels / Small Text

Plus Jakarta Sans

400 (Regular)

0.875rem (14px)

For form labels or smaller metadata.

2. Color Palette
The color scheme is based on a deep purple, a vibrant light green for accents, and neutral greys for balance.

Color

Hex Code

Usage

Primary (Dark Purple)

#2B203B

Footer background, dark UI elements.

Accent (Lime Green)

#BDEE96

Primary buttons, calls-to-action (CTAs).

Background (Off-White)

#F8F9FA

Main page background color.

Surface (White)

#FFFFFF

Card backgrounds, input fields.

Text (Dark)

#1E1E1E

Headings and body text.

Text (Light)

#FFFFFF

Text on dark backgrounds (e.g., in the footer).

Borders (Light Grey)

#E9ECEF

Borders for cards and input fields.

3. UI Components
These are the core building blocks of our interface.

Buttons
Buttons should be clean, with rounded corners to feel modern and approachable.

Primary Button:

Background Color: Accent (#BDEE96)

Text Color: Text (Dark) (#1E1E1E)

Padding: 12px 24px

Border Radius: 8px

Hover State: Slightly darken the background color or increase shadow.

Secondary/Footer Button:

Background Color: rgba(255, 255, 255, 0.1) (Transparent white on a dark background)

Text Color: Text (Light) (#FFFFFF)

Border: 1px solid #FFFFFF

Padding: 10px 20px

Border Radius: 8px

Hover State: Background becomes less transparent.

Cards
Cards are used to group content for services, case studies, and industries.

Background Color: Surface (#FFFFFF)

Padding: 24px

Border: 1px solid #E9ECEF

Border Radius: 12px

Box Shadow: 0 4px 12px rgba(0, 0, 0, 0.05) (A soft, subtle shadow)

Input Fields
Used in forms like the contact section.

Background Color: Surface (#FFFFFF)

Border: 1px solid #E9ECEF

Border Radius: 8px

Padding: 12px

Focus State: The border color should change to the Accent (#BDEE96) or Primary color.

4. Layout & Spacing
Grid: Use a consistent grid system (like a 12-column grid) for overall page structure.

Whitespace: Be generous with whitespace. Use spacing to separate sections clearly. A good baseline for vertical space between sections is 80px to 100px.

Consistency: Use consistent spacing values (e.g., multiples of 8px) for padding and margins around elements to create a harmonious rhythm.